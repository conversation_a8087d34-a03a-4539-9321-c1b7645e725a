# TNGD Backup System

## Overview
The TNGD Backup System is a robust, reliable, and maintainable backup solution designed to handle daily, monthly, and historical backups of tables from the ddevo database to OSS storage. The system emphasizes sequential processing, comprehensive error handling, performance monitoring, and automated notifications to ensure 100% success rate.

**OSS Path Structure**: Backup files are organized using a configurable template: `Devo/{month_name_str}/week {week_number}/{date_str}/` for improved organization and scalability. See [OSS_PATH_STRUCTURE_UPDATE.md](OSS_PATH_STRUCTURE_UPDATE.md) for details.

## Key Features
- **Daily Backup:** Automated daily backups with sequential table processing, chunked data extraction, compression, and upload.
- **Monthly Backup:** Multiple backup strategies including day-by-day, week-by-week, hybrid adaptive, and emergency fallback.
- **Historical Backup:** Backup data for specific date ranges with day-by-day processing.
- **Error Handling:** Exponential backoff retry mechanism and detailed logging.
- **Notifications:** Email summaries with backup results and progress updates.
- **Performance Monitoring:** Resource usage tracking and alerting.
- **Cleanup:** Automatic cleanup of temporary files and disk space management.
- **Testing Framework:** Integrated tests for validating backup processes.

## Project Structure
- `run_daily_backup.bat` - Batch script to run daily backup system.
- `run_monthly_backup.bat` - Batch script to run monthly and historical backups.
- `scripts/` - Python scripts for backup scheduling, processing, testing, and setup.
- `core/` - Core modules including backup strategies, configuration, processing, and storage management.
- `utils/` - Utility modules for logging, notifications, disk cleanup, error handling, and validation.
- `tabletest/` - Table configuration and operations.
- `logs/` - Backup logs and checkpoints.

## Usage
- Run daily backup:
  ```
  run_daily_backup.bat
  ```
- Run monthly backup:
  ```
  run_monthly_backup.bat march 2025
  ```
- Run historical backup:
  ```
  run_monthly_backup.bat historical 2025-03-01 2025-03-31
  ```
- Run backup for a single table (daily):
  ```
  python scripts/daily_backup_scheduler.py --single-table
  ```
  This will process only one table for testing purposes, using the first table in the configuration.
  
- Run backup for a specific single table (daily):
  ```
  python scripts/daily_backup_scheduler.py my.app.tngd.waf
  ```
  Replace `my.app.tngd.waf` with the full table name you want to backup.

## Documentation
- Detailed system documentation is available in `DAILY_BACKUP_SYSTEM.md`.
- For setup and testing instructions, refer to scripts in the `scripts/` directory.

## Cleanup and Refactoring
- All legacy and deprecated code has been removed.
- Logging system consolidated to use `utils/minimal_logging.py`.
- Placeholder implementations replaced with production-ready code.
- Documentation updated to reflect current system state.

## Contact
For questions or support, please contact the development team.

---

This README replaces all previous versions to provide a clear and concise understanding of the current TNGD Backup System.
