#!/usr/bin/env python3
"""
OSS Connection Test Script
Test Alibaba Cloud OSS connectivity and list backup files
"""

import os
import sys
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import oss2
    from core.config_manager import ConfigManager
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all dependencies are installed")
    sys.exit(1)

def test_oss_connection():
    """Test OSS connection and list backup files."""
    try:
        # Load configuration
        config = ConfigManager()
        credentials = config.get_oss_credentials()
        
        print("🔧 OSS Configuration Test")
        print("=" * 50)
        print(f"Endpoint: {credentials['endpoint']}")
        print(f"Bucket: {credentials['bucket']}")
        print(f"Prefix: {credentials['prefix']}")
        print()
        
        # Create OSS client
        auth = oss2.Auth(credentials['access_key_id'], credentials['access_key_secret'])
        bucket = oss2.Bucket(auth, credentials['endpoint'], credentials['bucket'])
        
        print("✅ OSS Authentication: SUCCESS")
        
        # Test bucket access
        try:
            bucket_info = bucket.get_bucket_info()
            print(f"✅ Bucket Access: SUCCESS")
            print(f"   Storage Class: {bucket_info.storage_class}")
            print(f"   Creation Date: {bucket_info.creation_date}")
        except Exception as e:
            print(f"❌ Bucket Access: FAILED - {e}")
            return False
        
        print()
        print("📁 Listing backup files...")
        print("=" * 50)
        
        # List files in backup directory
        backup_prefix = "backup/daily/2025-06-19/"
        files_found = 0
        
        for obj in oss2.ObjectIterator(bucket, prefix=backup_prefix):
            files_found += 1
            size_mb = obj.size / (1024 * 1024)
            print(f"📄 {obj.key}")
            print(f"   Size: {size_mb:.1f} MB")
            print(f"   Modified: {obj.last_modified}")
            print(f"   ETag: {obj.etag}")
            print()
        
        if files_found == 0:
            print("⚠️  No backup files found in backup/daily/2025-06-19/")
            
            # Check if there are any files in the backup directory at all
            print("\n🔍 Checking for any backup files...")
            for obj in oss2.ObjectIterator(bucket, prefix="backup/", max_keys=10):
                size_mb = obj.size / (1024 * 1024)
                print(f"📄 {obj.key} ({size_mb:.1f} MB)")
        else:
            print(f"✅ Found {files_found} backup file(s) for 2025-06-19")
        
        # Test specific file that should have been uploaded
        target_file = "backup/daily/2025-06-19/my_app_tngd_waf_20250619_042344.tar.gz"
        print(f"\n🎯 Checking specific file: {target_file}")
        
        try:
            obj_info = bucket.head_object(target_file)
            size_mb = obj_info.content_length / (1024 * 1024)
            print(f"✅ File exists!")
            print(f"   Size: {size_mb:.1f} MB")
            print(f"   Last Modified: {obj_info.last_modified}")
            print(f"   ETag: {obj_info.etag}")
            print(f"   Content Type: {obj_info.content_type}")
        except oss2.exceptions.NoSuchKey:
            print(f"❌ File not found: {target_file}")
        except Exception as e:
            print(f"❌ Error checking file: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ OSS Connection Test Failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 TNGD OSS Connection Test")
    print("=" * 60)
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success = test_oss_connection()
    
    print()
    print("=" * 60)
    if success:
        print("✅ OSS Connection Test: PASSED")
    else:
        print("❌ OSS Connection Test: FAILED")
